<?php
if (!defined('ABSPATH')) {
    exit;
}

$is_edit = $button->id > 0;
$page_title = $is_edit ? __('Edit Email Button', 'woo-email-action-buttons') : __('Add Email Button', 'woo-email-action-buttons');
$submit_text = $is_edit ? __('Update Button', 'woo-email-action-buttons') : __('Create Button', 'woo-email-action-buttons');
?>

<div class="wrap">
    <h1 class="wp-heading-inline"><?php echo esc_html($page_title); ?></h1>
    
    <a href="<?php echo esc_url(admin_url('admin.php?page=woo-email-action-buttons')); ?>" class="page-title-action">
        <?php _e('← Back to List', 'woo-email-action-buttons'); ?>
    </a>
    
    <hr class="wp-header-end">
    
    <form method="post" action="<?php echo esc_url(admin_url('admin-post.php')); ?>" id="weab-button-form">
        <?php wp_nonce_field('weab_save_button', 'weab_nonce'); ?>
        <input type="hidden" name="action" value="weab_save_button">
        <?php if ($is_edit): ?>
            <input type="hidden" name="button_id" value="<?php echo esc_attr($button->id); ?>">
        <?php endif; ?>
        
        <table class="form-table" role="presentation">
            <tbody>
                <!-- Nome Pulsante -->
                <tr>
                    <th scope="row">
                        <label for="button_name"><?php _e('Nome Pulsante', 'woo-email-action-buttons'); ?> <span class="required">*</span></label>
                    </th>
                    <td>
                        <input type="text" 
                               id="button_name" 
                               name="button_name" 
                               value="<?php echo esc_attr($button->button_name); ?>" 
                               class="regular-text" 
                               required>
                        <p class="description">
                            <?php _e('Il nome che apparirà sul pulsante nella lista ordini.', 'woo-email-action-buttons'); ?>
                        </p>
                    </td>
                </tr>
                
                <!-- Descrizione -->
                <tr>
                    <th scope="row">
                        <label for="button_description"><?php _e('Descrizione', 'woo-email-action-buttons'); ?></label>
                    </th>
                    <td>
                        <textarea id="button_description" 
                                  name="button_description" 
                                  rows="3" 
                                  class="large-text"><?php echo esc_textarea($button->button_description); ?></textarea>
                        <p class="description">
                            <?php _e('Descrizione interna del pulsante (opzionale).', 'woo-email-action-buttons'); ?>
                        </p>
                    </td>
                </tr>
                
                <!-- Stati Ordine -->
                <tr>
                    <th scope="row">
                        <label><?php _e('Stati Ordine', 'woo-email-action-buttons'); ?> <span class="required">*</span></label>
                    </th>
                    <td>
                        <fieldset>
                            <legend class="screen-reader-text">
                                <?php _e('Seleziona gli stati ordine per cui mostrare questo pulsante', 'woo-email-action-buttons'); ?>
                            </legend>
                            
                            <?php foreach ($order_statuses as $status_key => $status_name): ?>
                                <label>
                                    <input type="checkbox" 
                                           name="order_statuses[]" 
                                           value="<?php echo esc_attr($status_key); ?>"
                                           <?php checked(in_array($status_key, (array)$button->order_statuses)); ?>>
                                    <?php echo esc_html($status_name); ?>
                                </label><br>
                            <?php endforeach; ?>
                            
                            <p class="description">
                                <?php _e('Seleziona gli stati ordine per cui questo pulsante deve essere visibile nella lista ordini.', 'woo-email-action-buttons'); ?>
                            </p>
                        </fieldset>
                    </td>
                </tr>
                
                <!-- Codice Dashicon -->
                <tr>
                    <th scope="row">
                        <label for="dashicon_code"><?php _e('Codice Dashicon', 'woo-email-action-buttons'); ?></label>
                    </th>
                    <td>
                        <input type="text" 
                               id="dashicon_code" 
                               name="dashicon_code" 
                               value="<?php echo esc_attr($button->dashicon_code); ?>" 
                               class="regular-text" 
                               placeholder="dashicons-email">
                        
                        <div class="dashicon-preview" style="margin-top: 10px;">
                            <span class="dashicons <?php echo esc_attr($button->dashicon_code); ?>" style="font-size: 20px;"></span>
                            <span class="preview-text"><?php _e('Anteprima icona', 'woo-email-action-buttons'); ?></span>
                        </div>
                        
                        <p class="description">
                            <?php _e('Codice dell\'icona Dashicons da utilizzare per il pulsante.', 'woo-email-action-buttons'); ?>
                            <a href="https://developer.wordpress.org/resource/dashicons/" target="_blank">
                                <?php _e('Visualizza tutte le icone disponibili', 'woo-email-action-buttons'); ?>
                            </a>
                        </p>
                    </td>
                </tr>
                
                <!-- Contenuto Email -->
                <tr>
                    <th scope="row">
                        <label for="email_content"><?php _e('Contenuto Email', 'woo-email-action-buttons'); ?> <span class="required">*</span></label>
                    </th>
                    <td>
                        <?php
                        wp_editor($button->email_content, 'email_content', array(
                            'textarea_name' => 'email_content',
                            'textarea_rows' => 10,
                            'media_buttons' => false,
                            'teeny' => false,
                            'quicktags' => true,
                            'tinymce' => array(
                                'toolbar1' => 'bold,italic,underline,strikethrough,|,bullist,numlist,|,link,unlink,|,undo,redo',
                                'toolbar2' => ''
                            )
                        ));
                        ?>
                        
                        <div class="weab-placeholders-help" style="margin-top: 15px; padding: 10px; background: #f9f9f9; border: 1px solid #ddd;">
                            <h4><?php _e('Placeholder disponibili:', 'woo-email-action-buttons'); ?></h4>
                            <div class="placeholder-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px;">
                                <?php foreach (Email_Action_Buttons_Email::get_available_placeholders() as $placeholder => $description): ?>
                                    <div class="placeholder-item">
                                        <code style="background: #fff; padding: 2px 4px; border-radius: 2px; cursor: pointer;" 
                                              onclick="insertPlaceholder('<?php echo esc_js($placeholder); ?>')"
                                              title="<?php _e('Clicca per inserire', 'woo-email-action-buttons'); ?>">
                                            <?php echo esc_html($placeholder); ?>
                                        </code>
                                        <br>
                                        <small><?php echo esc_html($description); ?></small>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                        
                        <p class="description">
                            <?php _e('Il contenuto dell\'email che verrà inviata al cliente. Puoi utilizzare i placeholder sopra per includere informazioni dinamiche dell\'ordine.', 'woo-email-action-buttons'); ?>
                        </p>
                    </td>
                </tr>
            </tbody>
        </table>
        
        <?php submit_button($submit_text, 'primary', 'submit', false); ?>
        
        <a href="<?php echo esc_url(admin_url('admin.php?page=woo-email-action-buttons')); ?>" class="button button-secondary" style="margin-left: 10px;">
            <?php _e('Annulla', 'woo-email-action-buttons'); ?>
        </a>
    </form>
</div>

<script type="text/javascript">
jQuery(document).ready(function($) {
    // Aggiorna anteprima icona quando cambia il codice
    $('#dashicon_code').on('input', function() {
        var iconCode = $(this).val() || 'dashicons-email';
        $('.dashicon-preview .dashicons').attr('class', 'dashicons ' + iconCode);
    });

    // Validazione form
    $('#weab-button-form').on('submit', function(e) {
        var buttonName = $('#button_name').val().trim();
        var emailContent = '';

        // Ottieni contenuto dall'editor
        if (typeof tinyMCE !== 'undefined' && tinyMCE.get('email_content')) {
            emailContent = tinyMCE.get('email_content').getContent();
        } else {
            emailContent = $('#email_content').val();
        }

        var selectedStatuses = $('input[name="order_statuses[]"]:checked').length;

        if (!buttonName) {
            alert('<?php _e('Il nome del pulsante è obbligatorio.', 'woo-email-action-buttons'); ?>');
            e.preventDefault();
            return false;
        }

        if (!emailContent.trim()) {
            alert('<?php _e('Il contenuto dell\'email è obbligatorio.', 'woo-email-action-buttons'); ?>');
            e.preventDefault();
            return false;
        }

        if (selectedStatuses === 0) {
            alert('<?php _e('Seleziona almeno uno stato ordine.', 'woo-email-action-buttons'); ?>');
            e.preventDefault();
            return false;
        }
    });
});

// Funzione per inserire placeholder nell'editor
function insertPlaceholder(placeholder) {
    if (typeof tinyMCE !== 'undefined' && tinyMCE.get('email_content')) {
        tinyMCE.get('email_content').execCommand('mceInsertContent', false, placeholder);
    } else {
        var textarea = document.getElementById('email_content');
        var cursorPos = textarea.selectionStart;
        var textBefore = textarea.value.substring(0, cursorPos);
        var textAfter = textarea.value.substring(cursorPos);
        textarea.value = textBefore + placeholder + textAfter;
        textarea.selectionStart = textarea.selectionEnd = cursorPos + placeholder.length;
        textarea.focus();
    }
}
</script>

<style>
.required {
    color: #d63638;
}

.placeholder-item {
    margin-bottom: 5px;
}

.placeholder-item code:hover {
    background: #e1f5fe !important;
}

.dashicon-preview {
    display: flex;
    align-items: center;
    gap: 10px;
}

.preview-text {
    color: #666;
    font-style: italic;
}

.weab-placeholders-help {
    border-radius: 4px;
}

.weab-placeholders-help h4 {
    margin-top: 0;
    margin-bottom: 10px;
}
</style>

<script type="text/javascript">
jQuery(document).ready(function($) {
    // Aggiorna anteprima icona quando cambia il codice
    $('#dashicon_code').on('input', function() {
        var iconCode = $(this).val() || 'dashicons-email';
        $('.dashicon-preview .dashicons').attr('class', 'dashicons ' + iconCode);
    });
    
    // Validazione form
    $('#weab-button-form').on('submit', function(e) {
        var buttonName = $('#button_name').val().trim();
        var emailContent = '';
        
        // Ottieni contenuto dall'editor
        if (typeof tinyMCE !== 'undefined' && tinyMCE.get('email_content')) {
            emailContent = tinyMCE.get('email_content').getContent();
        } else {
            emailContent = $('#email_content').val();
        }
        
        var selectedStatuses = $('input[name="order_statuses[]"]:checked').length;
        
        if (!buttonName) {
            alert('<?php _e('Il nome del pulsante è obbligatorio.', 'woo-email-action-buttons'); ?>');
            e.preventDefault();
            return false;
        }
        
        if (!emailContent.trim()) {
            alert('<?php _e('Il contenuto dell\'email è obbligatorio.', 'woo-email-action-buttons'); ?>');
            e.preventDefault();
            return false;
        }
        
        if (selectedStatuses === 0) {
            alert('<?php _e('Seleziona almeno uno stato ordine.', 'woo-email-action-buttons'); ?>');
            e.preventDefault();
            return false;
        }
    });
});

// Funzione per inserire placeholder nell'editor
function insertPlaceholder(placeholder) {
    if (typeof tinyMCE !== 'undefined' && tinyMCE.get('email_content')) {
        tinyMCE.get('email_content').execCommand('mceInsertContent', false, placeholder);
    } else {
        var textarea = document.getElementById('email_content');
        var cursorPos = textarea.selectionStart;
        var textBefore = textarea.value.substring(0, cursorPos);
        var textAfter = textarea.value.substring(cursorPos);
        textarea.value = textBefore + placeholder + textAfter;
        textarea.selectionStart = textarea.selectionEnd = cursorPos + placeholder.length;
        textarea.focus();
    }
}
</script>

<style>
.required {
    color: #d63638;
}

.placeholder-item {
    margin-bottom: 5px;
}

.placeholder-item code:hover {
    background: #e1f5fe !important;
}

.dashicon-preview {
    display: flex;
    align-items: center;
    gap: 10px;
}

.preview-text {
    color: #666;
    font-style: italic;
}

.weab-placeholders-help {
    border-radius: 4px;
}

.weab-placeholders-help h4 {
    margin-top: 0;
    margin-bottom: 10px;
}
</style>
