<?php
/**
 * Plugin Name: WooCommerce Email Action Buttons
 * Plugin URI: 
 * Description: Aggiunge pulsanti di azione email personalizzati alla lista ordini di WooCommerce per inviare email ai clienti.
 * Version: 1.0.0
 * Author: 
 * Text Domain: woo-email-action-buttons
 * Domain Path: /languages
 * Requires at least: 5.8
 * Requires PHP: 8.0
 * WC requires at least: 6.0
 * WC tested up to: 8.0
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

// Verifica che WooCommerce sia attivo
if (!in_array('woocommerce/woocommerce.php', apply_filters('active_plugins', get_option('active_plugins'))) && !function_exists('WC')) {
    add_action('admin_notices', function() {
        echo '<div class="error"><p>WooCommerce Email Action Buttons richiede WooCommerce per funzionare.</p></div>';
    });
    return;
}

// Definizione costanti
define('WEAB_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('WEAB_PLUGIN_URL', plugin_dir_url(__FILE__));
define('WEAB_VERSION', '1.0.0');


// Caricamento file delle classi
require_once WEAB_PLUGIN_DIR . 'includes/class-email-action-buttons-db.php';
require_once WEAB_PLUGIN_DIR . 'includes/class-email-action-buttons-admin.php';
require_once WEAB_PLUGIN_DIR . 'includes/class-email-action-buttons-email.php';
require_once WEAB_PLUGIN_DIR . 'includes/class-email-action-buttons-integration.php';



/**
 * Inizializzazione del plugin
 */
function weab_init() {
    // Verifica che WooCommerce sia completamente caricato
    if (!class_exists('WooCommerce') && !function_exists('WC')) {
        // Se WooCommerce non è ancora caricato, riprova più tardi
        add_action('woocommerce_init', 'weab_init');
        return;
    }

    // Inizializza le classi solo se siamo nell'admin
    if (is_admin()) {
        new Email_Action_Buttons_Admin();
        new Email_Action_Buttons_Integration();
    }

    // Inizializza sempre la classe email per gestire le richieste
    new Email_Action_Buttons_Email();
}
add_action('plugins_loaded', 'weab_init');

/**
 * Attivazione del plugin
 */
register_activation_hook(__FILE__, 'weab_activate');
function weab_activate() {
    // Verifica che WooCommerce sia attivo
    if (!class_exists('WooCommerce')) {
        deactivate_plugins(plugin_basename(__FILE__));
        wp_die('Questo plugin richiede WooCommerce per funzionare.');
    }
    
    // Crea le tabelle del database
    $db = new Email_Action_Buttons_DB();
    $db->create_tables();
    
    // Flush rewrite rules
    flush_rewrite_rules();
}

/**
 * Disattivazione del plugin
 */
register_deactivation_hook(__FILE__, 'weab_deactivate');
function weab_deactivate() {
    // Flush rewrite rules
    flush_rewrite_rules();
}

/**
 * Funzione helper per ottenere l'istanza del database
 */
function weab_get_db() {
    return new Email_Action_Buttons_DB();
}

/**
 * Funzione helper per il logging degli errori
 */
function weab_log($message, $level = 'info') {
    if (defined('WP_DEBUG') && WP_DEBUG) {
        error_log('[WooCommerce Email Action Buttons] ' . $message);
    }
}

/**
 * Funzione helper per verificare le capacità dell'utente
 */
function weab_user_can_manage() {
    return current_user_can('manage_woocommerce');
}

/**
 * Funzione helper per sanitizzare l'input
 */
function weab_sanitize_input($input, $type = 'text') {
    switch ($type) {
        case 'text':
            return sanitize_text_field($input);
        case 'textarea':
            return sanitize_textarea_field($input);
        case 'email':
            return sanitize_email($input);
        case 'url':
            return esc_url_raw($input);
        case 'html':
            return wp_kses_post($input);
        case 'array':
            return is_array($input) ? array_map('sanitize_text_field', $input) : array();
        default:
            return sanitize_text_field($input);
    }
}

/**
 * Funzione helper per ottenere tutti gli stati ordine WooCommerce
 */
function weab_get_order_statuses() {
    $statuses = wc_get_order_statuses();
    return $statuses;
}

/**
 * Funzione helper per verificare se un ordine ha uno stato specifico
 */
function weab_order_has_status($order, $statuses) {
    if (!is_array($statuses) || empty($statuses)) {
        return false;
    }
    
    $order_status = 'wc-' . $order->get_status();
    return in_array($order_status, $statuses);
}
