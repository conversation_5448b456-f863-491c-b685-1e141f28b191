<?php
if (!defined('ABSPATH')) {
    exit;
}
?>

<div class="wrap">
    <h1 class="wp-heading-inline">
        <?php _e('Email Action Buttons', 'woo-email-action-buttons'); ?>
    </h1>

    <a href="<?php echo esc_url(add_query_arg(array('action' => 'add'), admin_url('admin.php?page=woo-email-action-buttons'))); ?>" class="page-title-action">
        <?php _e('Add New', 'woo-email-action-buttons'); ?>
    </a>
    
    <hr class="wp-header-end">
    
    <?php if (empty($buttons)): ?>
        <div class="notice notice-info">
            <p>
                <?php _e('You haven\'t created any email buttons yet.', 'woo-email-action-buttons'); ?>
                <a href="<?php echo esc_url(add_query_arg(array('action' => 'add'), admin_url('admin.php?page=woo-email-action-buttons'))); ?>">
                    <?php _e('Create one now', 'woo-email-action-buttons'); ?>
                </a>
            </p>
        </div>
    <?php else: ?>
        <table class="wp-list-table widefat fixed striped">
            <thead>
                <tr>
                    <th scope="col" class="manage-column column-name column-primary">
                        <?php _e('Button Name', 'woo-email-action-buttons'); ?>
                    </th>
                    <th scope="col" class="manage-column column-description">
                        <?php _e('Description', 'woo-email-action-buttons'); ?>
                    </th>
                    <th scope="col" class="manage-column column-statuses">
                        <?php _e('Order Statuses', 'woo-email-action-buttons'); ?>
                    </th>
                    <th scope="col" class="manage-column column-icon">
                        <?php _e('Icon', 'woo-email-action-buttons'); ?>
                    </th>
                    <th scope="col" class="manage-column column-date">
                        <?php _e('Creation Date', 'woo-email-action-buttons'); ?>
                    </th>
                    <th scope="col" class="manage-column column-actions">
                        <?php _e('Actions', 'woo-email-action-buttons'); ?>
                    </th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($buttons as $button): ?>
                    <tr>
                        <td class="column-name column-primary">
                            <strong>
                                <a href="<?php echo esc_url(add_query_arg(array('action' => 'edit', 'button_id' => $button->id), admin_url('admin.php?page=woo-email-action-buttons'))); ?>">
                                    <?php echo esc_html($button->button_name); ?>
                                </a>
                            </strong>
                            <div class="row-actions">
                                <span class="edit">
                                    <a href="<?php echo esc_url(add_query_arg(array('action' => 'edit', 'button_id' => $button->id), admin_url('admin.php?page=woo-email-action-buttons'))); ?>">
                                        <?php _e('Modifica', 'woo-email-action-buttons'); ?>
                                    </a>
                                </span>
                                |
                                <span class="trash">
                                    <a href="<?php echo esc_url(wp_nonce_url(admin_url('admin-post.php?action=weab_delete_button&button_id=' . $button->id), 'weab_delete_button_' . $button->id, 'weab_nonce')); ?>" 
                                       onclick="return confirm('<?php _e('Sei sicuro di voler eliminare questo pulsante?', 'woo-email-action-buttons'); ?>')">
                                        <?php _e('Elimina', 'woo-email-action-buttons'); ?>
                                    </a>
                                </span>
                            </div>
                        </td>
                        <td class="column-description">
                            <?php echo esc_html(wp_trim_words($button->button_description, 10)); ?>
                        </td>
                        <td class="column-statuses">
                            <?php 
                            if (is_array($button->order_statuses) && !empty($button->order_statuses)) {
                                $status_names = array();
                                $all_statuses = weab_get_order_statuses();
                                
                                foreach ($button->order_statuses as $status) {
                                    if (isset($all_statuses[$status])) {
                                        $status_names[] = $all_statuses[$status];
                                    }
                                }
                                
                                echo esc_html(implode(', ', $status_names));
                            } else {
                                echo '<em>' . __('Nessuno stato selezionato', 'woo-email-action-buttons') . '</em>';
                            }
                            ?>
                        </td>
                        <td class="column-icon">
                            <span class="dashicons <?php echo esc_attr($button->dashicon_code); ?>" title="<?php echo esc_attr($button->dashicon_code); ?>"></span>
                        </td>
                        <td class="column-date">
                            <?php echo esc_html(date_i18n(get_option('date_format'), strtotime($button->created_at))); ?>
                        </td>
                        <td class="column-actions">
                            <a href="<?php echo esc_url(add_query_arg(array('action' => 'edit', 'button_id' => $button->id), admin_url('admin.php?page=woo-email-action-buttons'))); ?>" class="button button-small">
                                <?php _e('Modifica', 'woo-email-action-buttons'); ?>
                            </a>
                        </td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    <?php endif; ?>
    
    <div class="weab-info-box" style="margin-top: 20px; padding: 15px; background: #f1f1f1; border-left: 4px solid #0073aa;">
        <h3><?php _e('Come funziona', 'woo-email-action-buttons'); ?></h3>
        <p><?php _e('I pulsanti email che crei qui appariranno nella lista ordini di WooCommerce per gli ordini che corrispondono agli stati selezionati. Cliccando su un pulsante, verrà inviata un\'email personalizzata al cliente.', 'woo-email-action-buttons'); ?></p>
        
        <h4><?php _e('Placeholder disponibili per il contenuto email:', 'woo-email-action-buttons'); ?></h4>
        <ul style="list-style-type: disc; margin-left: 20px;">
            <?php foreach (Email_Action_Buttons_Email::get_available_placeholders() as $placeholder => $description): ?>
                <li><code><?php echo esc_html($placeholder); ?></code> - <?php echo esc_html($description); ?></li>
            <?php endforeach; ?>
        </ul>
        
        <h4><?php _e('Icone Dashicons:', 'woo-email-action-buttons'); ?></h4>
        <p>
            <?php _e('Puoi utilizzare qualsiasi icona Dashicons per i tuoi pulsanti.', 'woo-email-action-buttons'); ?>
            <a href="https://developer.wordpress.org/resource/dashicons/" target="_blank">
                <?php _e('Visualizza tutte le icone disponibili', 'woo-email-action-buttons'); ?>
            </a>
        </p>
    </div>
</div>

<style>
.weab-info-box {
    border-radius: 4px;
}

.weab-info-box h3 {
    margin-top: 0;
    color: #0073aa;
}

.weab-info-box code {
    background: #fff;
    padding: 2px 4px;
    border-radius: 2px;
    font-family: monospace;
}

.column-icon {
    width: 60px;
    text-align: center;
}

.column-date {
    width: 120px;
}

.column-actions {
    width: 100px;
}

.dashicons {
    font-size: 20px;
    width: 20px;
    height: 20px;
}
</style>
