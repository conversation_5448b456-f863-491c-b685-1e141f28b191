/* WooCommerce Email Action Buttons - Admin Styles */

/* Lista pulsanti */
.weab-info-box {
    margin-top: 20px;
    padding: 15px;
    background: #f1f1f1;
    border-left: 4px solid #0073aa;
    border-radius: 4px;
}

.weab-info-box h3 {
    margin-top: 0;
    color: #0073aa;
}

.weab-info-box code {
    background: #fff;
    padding: 2px 4px;
    border-radius: 2px;
    font-family: monospace;
}

.column-icon {
    width: 60px;
    text-align: center;
}

.column-date {
    width: 120px;
}

.column-actions {
    width: 100px;
}

.dashicons {
    font-size: 20px;
    width: 20px;
    height: 20px;
}

/* Form pulsante */
.required {
    color: #d63638;
}

.placeholder-item {
    margin-bottom: 5px;
}

.placeholder-item code {
    background: #fff;
    padding: 2px 4px;
    border-radius: 2px;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.placeholder-item code:hover {
    background: #e1f5fe !important;
}

.dashicon-preview {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-top: 10px;
}

.preview-text {
    color: #666;
    font-style: italic;
}

.weab-placeholders-help {
    margin-top: 15px;
    padding: 10px;
    background: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.weab-placeholders-help h4 {
    margin-top: 0;
    margin-bottom: 10px;
}

.placeholder-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 10px;
}

/* Stili per i pulsanti nella lista ordini WooCommerce */
/* I CSS specifici per le icone vengono generati dinamicamente dalla classe Integration */

/* Stili generici per tutti i pulsanti email action */
[class*="wc-action-button-weab_button_"] {
    position: relative;
    display: inline-block;
    width: 2em;
    height: 2em;
    line-height: 2em;
    text-align: center;
    border-radius: 3px;
    text-decoration: none;
    margin: 0 2px;
    background: #0073aa;
    color: #fff;
    transition: background-color 0.2s ease;
}

[class*="wc-action-button-weab_button_"]:hover {
    background: #005a87;
    color: #fff;
}

[class*="wc-action-button-weab_button_"]::after {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 16px;
    line-height: 1;
}

/* Responsive design */
@media screen and (max-width: 782px) {
    .placeholder-grid {
        grid-template-columns: 1fr;
    }
    
    .dashicon-preview {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }
    
    .column-icon,
    .column-date,
    .column-actions {
        width: auto;
    }
}

/* Miglioramenti per l'accessibilità */
.weab-info-box a:focus,
.placeholder-item code:focus {
    outline: 2px solid #0073aa;
    outline-offset: 2px;
}

/* Stili per i messaggi di errore e successo */
.notice.weab-notice {
    border-left-width: 4px;
    padding: 12px;
}

.notice.weab-notice.notice-success {
    border-left-color: #00a32a;
    background-color: #f0f6fc;
}

.notice.weab-notice.notice-error {
    border-left-color: #d63638;
    background-color: #fcf0f1;
}

/* Stili per la tabella della lista pulsanti */
.wp-list-table .column-name {
    width: 25%;
}

.wp-list-table .column-description {
    width: 30%;
}

.wp-list-table .column-statuses {
    width: 25%;
}

.wp-list-table .column-icon {
    width: 8%;
}

.wp-list-table .column-date {
    width: 12%;
}

/* Miglioramenti per il form */
.form-table th {
    width: 200px;
    vertical-align: top;
    padding-top: 15px;
}

.form-table td {
    padding-top: 10px;
    padding-bottom: 15px;
}

.form-table .description {
    margin-top: 5px;
    color: #666;
    font-style: italic;
}

/* Stili per i checkbox degli stati ordine */
.form-table fieldset label {
    display: block;
    margin-bottom: 8px;
    font-weight: normal;
}

.form-table fieldset input[type="checkbox"] {
    margin-right: 8px;
}

/* Animazioni */
.placeholder-item code {
    transition: all 0.2s ease;
}

.dashicon-preview .dashicons {
    transition: color 0.2s ease;
}

/* Stili per il selettore Unicode */
.weab-unicode-selector .unicode-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    gap: 10px;
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid #ddd;
    padding: 10px;
    background: #f9f9f9;
    border-radius: 4px;
}

.weab-unicode-selector .unicode-option {
    text-align: center;
    cursor: pointer;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 3px;
    background: white;
    transition: all 0.2s ease;
}

.weab-unicode-selector .unicode-option:hover {
    border-color: #0073aa;
    background: #f0f8ff;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.weab-unicode-selector .unicode-option.selected {
    border-color: #0073aa !important;
    background: #e1f5fe !important;
    box-shadow: 0 0 0 1px #0073aa;
}

.weab-unicode-selector .unicode-option small {
    font-size: 10px;
    color: #666;
    line-height: 1.2;
}

.weab-unicode-selector .unicode-preview {
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 10px 0;
    padding: 10px;
    background: #f8f9fa;
    border: 1px solid #e1e1e1;
    border-radius: 4px;
}

.weab-unicode-selector .unicode-icon {
    font-size: 24px;
    color: #0073aa;
}

.weab-unicode-selector .unicode-display {
    font-family: monospace;
    color: #666;
    font-size: 12px;
}

/* Stili per i placeholder cliccabili */
.weab-placeholders-help {
    border-radius: 4px;
}

.weab-placeholders-help .placeholder-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: 12px;
}

.weab-placeholders-help .placeholder-item {
    background: white;
    padding: 10px;
    border-radius: 4px;
    border: 1px solid #ddd;
}

.weab-placeholders-help .clickable-placeholder {
    background: #0073aa;
    color: white;
    padding: 4px 8px;
    border-radius: 3px;
    cursor: pointer;
    display: inline-block;
    margin-bottom: 5px;
    font-weight: bold;
    transition: all 0.2s ease;
    border: none;
    font-family: monospace;
}

.weab-placeholders-help .clickable-placeholder:hover {
    background: #005a87;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.weab-placeholders-help .clickable-placeholder:active {
    transform: translateY(0);
    box-shadow: 0 1px 2px rgba(0,0,0,0.2);
}

/* Print styles */
@media print {
    .weab-info-box,
    .page-title-action,
    .button {
        display: none;
    }
}
