<?php
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Classe per la gestione dell'interfaccia admin
 */
class Email_Action_Buttons_Admin {
    
    private $db;
    
    public function __construct() {
        $this->db = new Email_Action_Buttons_DB();
        
        // Aggiungi menu admin
        add_action('admin_menu', array($this, 'add_admin_menu'));
        
        // Registra assets admin
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_assets'));
        
        // Gestisci le azioni del form
        add_action('admin_post_weab_save_button', array($this, 'handle_save_button'));
        add_action('admin_post_weab_delete_button', array($this, 'handle_delete_button'));
        
        // Aggiungi notice per feedback utente
        add_action('admin_notices', array($this, 'show_admin_notices'));
    }
    
    /**
     * Aggiunge il menu admin
     */
    public function add_admin_menu() {
        add_submenu_page(
            'woocommerce',
            'Pulsanti Azione Email',
            'Pulsanti Azione Email',
            'manage_woocommerce',
            'woo-email-action-buttons',
            array($this, 'render_admin_page')
        );
    }
    
    /**
     * Carica gli assets admin
     */
    public function enqueue_admin_assets($hook) {
        if ('woocommerce_page_woo-email-action-buttons' !== $hook) {
            return;
        }
        
        // CSS admin
        wp_enqueue_style(
            'weab-admin-style',
            WEAB_PLUGIN_URL . 'assets/css/admin.css',
            array(),
            WEAB_VERSION
        );
        
        // WordPress editor
        wp_enqueue_editor();
        
        // Dashicons
        wp_enqueue_style('dashicons');
    }
    
    /**
     * Renderizza la pagina admin principale
     */
    public function render_admin_page() {
        if (!weab_user_can_manage()) {
            wp_die('Non hai i permessi per accedere a questa pagina.');
        }
        
        $action = isset($_GET['action']) ? sanitize_text_field($_GET['action']) : 'list';
        $button_id = isset($_GET['button_id']) ? intval($_GET['button_id']) : 0;
        
        switch ($action) {
            case 'add':
                $this->render_add_button_page();
                break;
            case 'edit':
                $this->render_edit_button_page($button_id);
                break;
            default:
                $this->render_buttons_list();
                break;
        }
    }
    
    /**
     * Renderizza la lista dei pulsanti
     */
    private function render_buttons_list() {
        $buttons = $this->db->get_all_buttons();
        include WEAB_PLUGIN_DIR . 'templates/admin-buttons-list.php';
    }
    
    /**
     * Renderizza la pagina di aggiunta pulsante
     */
    private function render_add_button_page() {
        $button = (object) array(
            'id' => 0,
            'button_name' => '',
            'button_description' => '',
            'order_statuses' => array(),
            'email_content' => '',
            'dashicon_code' => 'dashicons-email'
        );
        
        $order_statuses = weab_get_order_statuses();
        include WEAB_PLUGIN_DIR . 'templates/admin-button-form.php';
    }
    
    /**
     * Renderizza la pagina di modifica pulsante
     */
    private function render_edit_button_page($button_id) {
        $button = $this->db->get_button($button_id);
        
        if (!$button) {
            wp_die('Pulsante non trovato.');
        }
        
        $order_statuses = weab_get_order_statuses();
        include WEAB_PLUGIN_DIR . 'templates/admin-button-form.php';
    }
    
    /**
     * Gestisce il salvataggio del pulsante
     */
    public function handle_save_button() {
        if (!weab_user_can_manage()) {
            wp_die('Non hai i permessi per eseguire questa azione.');
        }

        // Verifica nonce
        if (!wp_verify_nonce($_POST['weab_nonce'], 'weab_save_button')) {
            wp_die('Errore di sicurezza. Riprova.');
        }
        
        // Validazione dati
        $errors = $this->validate_button_data($_POST);
        
        if (!empty($errors)) {
            $error_message = implode('<br>', $errors);
            wp_redirect(add_query_arg(array(
                'page' => 'woo-email-action-buttons',
                'action' => isset($_POST['button_id']) && $_POST['button_id'] > 0 ? 'edit' : 'add',
                'button_id' => isset($_POST['button_id']) ? intval($_POST['button_id']) : '',
                'error' => urlencode($error_message)
            ), admin_url('admin.php')));
            exit;
        }
        
        // Gestisci gli stati ordine
        $order_statuses = array();
        if (isset($_POST['show_on_all_statuses']) && $_POST['show_on_all_statuses'] == '1') {
            // Se "Mostra su tutti gli stati" è selezionato, includi tutti gli stati disponibili
            $all_statuses = wc_get_order_statuses();
            $order_statuses = array_keys($all_statuses);
        } else {
            // Altrimenti usa solo gli stati selezionati manualmente
            $order_statuses = isset($_POST['order_statuses']) ? $_POST['order_statuses'] : array();
        }

        // Prepara i dati
        $data = array(
            'button_name' => $_POST['button_name'],
            'button_description' => $_POST['button_description'],
            'order_statuses' => $order_statuses,
            'email_content' => $_POST['email_content'],
            'dashicon_code' => $_POST['dashicon_code']
        );
        
        $button_id = isset($_POST['button_id']) ? intval($_POST['button_id']) : 0;
        
        if ($button_id > 0) {
            // Aggiorna pulsante esistente
            $result = $this->db->update_button($button_id, $data);
            $message = $result !== false ? 'updated' : 'error';
        } else {
            // Crea nuovo pulsante
            $result = $this->db->insert_button($data);
            $message = $result ? 'created' : 'error';
        }
        
        // Redirect con messaggio
        wp_redirect(add_query_arg(array(
            'page' => 'woo-email-action-buttons',
            'message' => $message
        ), admin_url('admin.php')));
        exit;
    }
    
    /**
     * Gestisce l'eliminazione del pulsante
     */
    public function handle_delete_button() {
        if (!weab_user_can_manage()) {
            wp_die('Non hai i permessi per eseguire questa azione.');
        }

        $button_id = isset($_GET['button_id']) ? intval($_GET['button_id']) : 0;

        // Verifica nonce
        if (!wp_verify_nonce($_GET['weab_nonce'], 'weab_delete_button_' . $button_id)) {
            wp_die('Errore di sicurezza. Riprova.');
        }
        
        $result = $this->db->delete_button($button_id);
        $message = $result ? 'deleted' : 'error';
        
        // Redirect con messaggio
        wp_redirect(add_query_arg(array(
            'page' => 'woo-email-action-buttons',
            'message' => $message
        ), admin_url('admin.php')));
        exit;
    }
    
    /**
     * Valida i dati del pulsante
     */
    private function validate_button_data($data) {
        $errors = array();
        
        if (empty($data['button_name'])) {
            $errors[] = 'Il nome del pulsante è obbligatorio.';
        }

        if (empty($data['email_content'])) {
            $errors[] = 'Il contenuto dell\'email è obbligatorio.';
        }

        if (empty($data['order_statuses']) || !is_array($data['order_statuses'])) {
            $errors[] = 'Seleziona almeno uno stato ordine.';
        }
        
        if (empty($data['dashicon_code'])) {
            $data['dashicon_code'] = 'dashicons-email';
        }
        
        return $errors;
    }
    
    /**
     * Mostra i messaggi admin
     */
    public function show_admin_notices() {
        if (!isset($_GET['page']) || $_GET['page'] !== 'woo-email-action-buttons') {
            return;
        }
        
        $message = isset($_GET['message']) ? sanitize_text_field($_GET['message']) : '';
        $error = isset($_GET['error']) ? urldecode($_GET['error']) : '';
        
        if ($message) {
            $messages = array(
                'created' => 'Pulsante creato con successo.',
                'updated' => 'Pulsante aggiornato con successo.',
                'deleted' => 'Pulsante eliminato con successo.',
                'email_sent' => 'Email inviata con successo.'
            );
            
            if (isset($messages[$message])) {
                echo '<div class="notice notice-success is-dismissible"><p>' . $messages[$message] . '</p></div>';
            }
        }
        
        if ($error) {
            echo '<div class="notice notice-error is-dismissible"><p>' . $error . '</p></div>';
        }
    }

    // Metodo show_plugin_conflict_notices rimosso - avvisi debug non più necessari
}
