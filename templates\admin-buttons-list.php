<?php
if (!defined('ABSPATH')) {
    exit;
}
?>

<div class="wrap">
    <h1 class="wp-heading-inline">
        Pulsanti Azione Email
    </h1>

    <a href="<?php echo esc_url(add_query_arg(array('action' => 'add'), admin_url('admin.php?page=woo-email-action-buttons'))); ?>" class="page-title-action">
        Aggiungi Nuovo
    </a>
    
    <hr class="wp-header-end">
    
    <?php if (empty($buttons)): ?>
        <div class="notice notice-info">
            <p>
                Non hai ancora creato nessun pulsante email.
                <a href="<?php echo esc_url(add_query_arg(array('action' => 'add'), admin_url('admin.php?page=woo-email-action-buttons'))); ?>">
                    Creane uno ora
                </a>
            </p>
        </div>
    <?php else: ?>
        <table class="wp-list-table widefat fixed striped">
            <thead>
                <tr>
                    <th scope="col" class="manage-column column-name column-primary">
                        Nome Pulsante
                    </th>
                    <th scope="col" class="manage-column column-description">
                        Descrizione
                    </th>
                    <th scope="col" class="manage-column column-statuses">
                        Stati Ordine
                    </th>
                    <th scope="col" class="manage-column column-icon">
                        Icona
                    </th>
                    <th scope="col" class="manage-column column-date">
                        Data Creazione
                    </th>
                    <th scope="col" class="manage-column column-actions">
                        Azioni
                    </th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($buttons as $button): ?>
                    <tr>
                        <td class="column-name column-primary">
                            <strong>
                                <a href="<?php echo esc_url(add_query_arg(array('action' => 'edit', 'button_id' => $button->id), admin_url('admin.php?page=woo-email-action-buttons'))); ?>">
                                    <?php echo esc_html($button->button_name); ?>
                                </a>
                            </strong>
                            <div class="row-actions">
                                <span class="edit">
                                    <a href="<?php echo esc_url(add_query_arg(array('action' => 'edit', 'button_id' => $button->id), admin_url('admin.php?page=woo-email-action-buttons'))); ?>">
                                        Modifica
                                    </a>
                                </span>
                                |
                                <span class="trash">
                                    <a href="<?php echo esc_url(wp_nonce_url(admin_url('admin-post.php?action=weab_delete_button&button_id=' . $button->id), 'weab_delete_button_' . $button->id, 'weab_nonce')); ?>"
                                       onclick="return confirm('Sei sicuro di voler eliminare questo pulsante?')">
                                        Elimina
                                    </a>
                                </span>
                            </div>
                        </td>
                        <td class="column-description">
                            <?php echo esc_html(wp_trim_words($button->button_description, 10)); ?>
                        </td>
                        <td class="column-statuses">
                            <?php 
                            if (is_array($button->order_statuses) && !empty($button->order_statuses)) {
                                $status_names = array();
                                $all_statuses = weab_get_order_statuses();
                                
                                foreach ($button->order_statuses as $status) {
                                    if (isset($all_statuses[$status])) {
                                        $status_names[] = $all_statuses[$status];
                                    }
                                }
                                
                                echo esc_html(implode(', ', $status_names));
                            } else {
                                echo '<em>Nessuno stato selezionato</em>';
                            }
                            ?>
                        </td>
                        <td class="column-icon">
                            <?php
                            $unicode_code = $button->dashicon_code;
                            // Se è un codice unicode (con backslash singolo o doppio), mostralo direttamente
                            if (preg_match('/^\\\\{1,2}f[0-9a-fA-F]{3,4}$/i', $unicode_code)) {
                                // Rimuovi tutti i backslash iniziali
                                $clean_unicode = preg_replace('/^\\\\+/', '', $unicode_code);
                                echo '<span style="font-family: Dashicons; font-size: 20px;" title="' . esc_attr($unicode_code) . '">&#x' . esc_attr($clean_unicode) . ';</span>';
                            } else if (preg_match('/^f[0-9a-fA-F]{3,4}$/i', $unicode_code)) {
                                // Se inizia con 'f' ma senza backslash
                                echo '<span style="font-family: Dashicons; font-size: 20px;" title="' . esc_attr($unicode_code) . '">&#x' . esc_attr($unicode_code) . ';</span>';
                            } else if (preg_match('/^[0-9a-fA-F]{3,4}$/i', $unicode_code)) {
                                // Se è solo il codice hex senza 'f', aggiungilo
                                $clean_unicode = 'f' . $unicode_code;
                                echo '<span style="font-family: Dashicons; font-size: 20px;" title="f' . esc_attr($unicode_code) . '">&#x' . esc_attr($clean_unicode) . ';</span>';
                            } else {
                                // Fallback per vecchi pulsanti con nomi dashicon
                                echo '<span class="dashicons ' . esc_attr($unicode_code) . '" title="' . esc_attr($unicode_code) . '"></span>';
                            }
                            ?>
                        </td>
                        <td class="column-date">
                            <?php echo esc_html(date_i18n(get_option('date_format'), strtotime($button->created_at))); ?>
                        </td>
                        <td class="column-actions">
                            <a href="<?php echo esc_url(add_query_arg(array('action' => 'edit', 'button_id' => $button->id), admin_url('admin.php?page=woo-email-action-buttons'))); ?>" class="button button-small">
                                Modifica
                            </a>
                        </td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    <?php endif; ?>
    
    <div class="weab-info-box" style="margin-top: 20px; padding: 15px; background: #f1f1f1; border-left: 4px solid #0073aa;">
        <h3>Come funziona</h3>
        <p>I pulsanti email che crei qui appariranno nella lista ordini di WooCommerce per gli ordini che corrispondono agli stati selezionati. Cliccando su un pulsante, verrà inviata un'email personalizzata al cliente.</p>

        <h4>Placeholder disponibili per il contenuto email:</h4>
        <ul style="list-style-type: disc; margin-left: 20px;">
            <?php foreach (Email_Action_Buttons_Email::get_available_placeholders() as $placeholder => $description): ?>
                <li><code><?php echo esc_html($placeholder); ?></code> - <?php echo esc_html($description); ?></li>
            <?php endforeach; ?>
        </ul>
        
        <h4>Icone Dashicons:</h4>
        <p>
            Puoi utilizzare qualsiasi icona Dashicons per i tuoi pulsanti.
            <a href="https://developer.wordpress.org/resource/dashicons/" target="_blank">
                Visualizza tutte le icone disponibili
            </a>
        </p>
    </div>
</div>

<style>
.weab-info-box {
    border-radius: 4px;
}

.weab-info-box h3 {
    margin-top: 0;
    color: #0073aa;
}

.weab-info-box code {
    background: #fff;
    padding: 2px 4px;
    border-radius: 2px;
    font-family: monospace;
}

.column-icon {
    width: 60px;
    text-align: center;
}

.column-date {
    width: 120px;
}

.column-actions {
    width: 100px;
}

.dashicons {
    font-size: 20px;
    width: 20px;
    height: 20px;
}
</style>
